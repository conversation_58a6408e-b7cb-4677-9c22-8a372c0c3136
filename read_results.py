import torch
import matplotlib.pyplot as plt

# 加载结果文件
results = torch.load('SWIM-APPAFL-Integration/logs/new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1.pt')

# 查看结果文件中的内容
print("文件中包含的键:", results.keys())

# 打印最终准确率和损失
print(f"最终准确率: {results['final_accuracy']:.2f}%")
print(f"最终损失: {results['final_loss']:.4f}")

# 打印配置信息
print("\n配置信息:")
for key, value in results['config'].items():
    print(f"  {key}: {value}")

# 绘制准确率历史曲线
plt.figure(figsize=(10, 6))
plt.plot(results['accuracy_history'])
plt.title('new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1')
plt.xlabel('通信轮次')
plt.ylabel('准确率 (%)')
plt.grid(True)
plt.savefig('new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1.png')
plt.show()