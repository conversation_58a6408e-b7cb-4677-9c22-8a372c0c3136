#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的ResNet模型
"""

import torch
from models import ResNet18, ResNet50, ResNetCifarWithProjection, ResNet50CifarWithProjection

def test_models():
    """测试所有优化后的模型"""
    print('测试优化后的ResNet模型...')
    
    # 创建测试输入
    x = torch.randn(2, 3, 32, 32)
    
    try:
        # 测试ResNet18
        print("\n1. 测试ResNet18...")
        model18 = ResNet18(num_classes=10)
        out18 = model18(x)
        print(f'   ResNet18输出形状: {out18.shape}')
        
        # 测试ResNet50
        print("\n2. 测试ResNet50...")
        model50 = ResNet50(num_classes=10)
        out50 = model50(x)
        print(f'   ResNet50输出形状: {out50.shape}')
        
        # 测试带投影头的ResNet18
        print("\n3. 测试带投影头的ResNet18...")
        model18_proj = ResNetCifarWithProjection(out_dim=256, n_classes=10, resnet_type='resnet18')
        features, projection, classification = model18_proj(x)
        print(f'   特征形状: {features.shape}')
        print(f'   投影形状: {projection.shape}')
        print(f'   分类形状: {classification.shape}')
        
        # 测试带投影头的ResNet50
        print("\n4. 测试带投影头的ResNet50...")
        model50_proj = ResNet50CifarWithProjection(out_dim=256, n_classes=10)
        features, projection, classification = model50_proj(x)
        print(f'   特征形状: {features.shape}')
        print(f'   投影形状: {projection.shape}')
        print(f'   分类形状: {classification.shape}')
        
        print('\n✓ 所有模型测试通过！')
        
        # 测试模型参数数量
        print("\n模型参数统计:")
        print(f"ResNet18参数数量: {sum(p.numel() for p in model18.parameters()):,}")
        print(f"ResNet50参数数量: {sum(p.numel() for p in model50.parameters()):,}")
        print(f"ResNet18+投影头参数数量: {sum(p.numel() for p in model18_proj.parameters()):,}")
        print(f"ResNet50+投影头参数数量: {sum(p.numel() for p in model50_proj.parameters()):,}")
        
        return True
        
    except Exception as e:
        print(f'❌ 模型测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_models()
