PK                    ^  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/data.pklFB  �}q (X   final_accuracyqG@OK��Q�X
   final_lossqG?�9����X   accuracy_historyq]q(G@$      G@$      G@)\(�\G@%��G�{G@'�\(�G@*Q��RG@1J=p��
G@=z�G�G@3ffffffG@2�fffffG@7������G@8�fffffG@=������G@;Ǯz�HG@?�(�\G@?�fffffG@A5\(�G@<O\(�G@5�z�G�G@<������G@,��
=p�G@@�����G@?u\(�G@BJ=p��
G@D��Q�G@A��
=p�G@D/\(�G@?��Q�G@E�\(�G@D�fffffG@F�G�z�G@A�z�G�G@FU\(�G@@�z�G�G@B�
=p��G@F�=p��
G@Gٙ����G@=#�
=p�G@A�=p��
G@E�Q��G@I��Q�G@F�(�\G@L:�G�{G@H���Q�G@E�
=p��G@E��
=p�G@D�\(�G@D��\(��G@N|(�\G@5E�Q�G@F�fffffG@L��G�{G@Lb�\(��G@G+��Q�G@H�G�z�G@F�
=p�G@G��Q�G@G�z�G�G@J���RG@O'�z�HG@5��RG@H��G�{G@D�\(�G@H��\(��G@M�     G@I�     G@K�=p��
G@I      G@N^�Q�G@Ij=p��
G@Hz�G�G@N@     G@M�z�HG@N���
=qG@DB�\(��G@KZ�G�{G@N��
=p�G@L�\)G@J�p��
=G@L���
=qG@I�fffffG@I�z�HG@J�G�z�G@QG
=p��G@L��G�{G@K�G�z�G@O��G�{G@M��
=qG@L!G�z�G@O�����G@O�z�G�G@O�Q��G@Oz�G�{G@O"�\(��G@MxQ��G@Qz�G�G@K.z�G�G@OS33333G@O�
=p��G@OK��Q�eX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX   cifar10qX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�z�G�{X   train_batch_sizeqK@X   test_batch_sizeqK X   modelqX   resnetqX   use_swimqK X   out_dimqM X   temperatureqG?�      X   model_buffer_sizeqKX   krq G?ٙ�����X   async_weight_strategyq!X   local_roundsq"uu.PKۨެ�  �  PK                    e  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.format_versionFB ZZZZZZZZ1PK��܃      PK                    h ) new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.storage_alignmentFB% ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    _ 1 new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/byteorderFB- ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZlittlePK�=�      PK                    ] / new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/versionFB+ ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ3
PKўgU      PK                    l $ new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.data/serialization_idFB  ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400098244554116605436PK}� l(   (   PK          ۨެ�  �  ^                 new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/data.pklPK          ��܃      e             q  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.format_versionPK          ?wq�      h               new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.storage_alignmentPK          �=�      _             �  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/byteorderPK          ўgU      ]             �  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/versionPK          }� l(   (   l             R	  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.data/serialization_idPK,       -                       g      8
      PK    �
         PK      g  8
    