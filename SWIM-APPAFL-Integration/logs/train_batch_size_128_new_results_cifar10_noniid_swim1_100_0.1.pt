PK                    F  train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/data.pklFB ZZZZZZZZZZZZZZZZZZZZZZZZ�}q (X   final_accuracyqG@J��
=qX
   final_lossqG?�g�ۋW~X   accuracy_historyq]q(G@$      G@$      G@0s33333G@2��
=p�G@-G�z�HG@2(�\)G@2k��Q�G@4J=p��
G@5�G�z�G@4��
=p�G@4h�\)G@4ٙ����G@=��Q�G@8W
=p��G@<�\(�G@<�
=p��G@?}p��
=G@<aG�z�G@2}p��
=G@8��
=p�G@2s33333G@>(�\)G@9��
=qG@<�Q�G@<�z�G�G@>�z�HG@A>�Q�G@:�Q�G@@O\(�G@<�fffffG@@}p��
=G@=\(�\G@?z�G�G@>ffffffG@>
=p��
G@A�33333G@Bl�����G@1��
=qG@?�����G@>&fffffG@B�(�\G@Cj=p��
G@G=p��
=G@@��\)G@?�=p��
G@@��Q�G@C��G�{G@@�     G@DG�z�HG@2#�
=p�G@A,�����G@D�\(�G@E�\(�G@B��\)G@A%�Q�G@A7
=p��G@E!G�z�G@Eh�\)G@E�Q�G@I��
=qG@C�Q�G@BǮz�HG@EW
=p��G@E�fffffG@F\(�\G@C|(�\G@GG�z�HG@C�z�HG@H��Q�G@C0��
=qG@EB�\(��G@E�     G@F�(�\G@I�     G@@E�Q�G@F      G@F�(�\G@HH�\)G@G�(�\G@J�
=p�G@E�
=p��G@F�\(�G@H�p��
=G@I�\(��G@E�(�\G@G`     G@F��\)G@I�fffffG@I
=p��
G@L7
=p��G@G�
=p��G@KL�����G@Jl�����G@K������G@I˅�Q�G@JJ=p��
G@F!G�z�G@G��Q�G@I|(�\G@J��
=qeX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX   cifar10qX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�z�G�{X   train_batch_sizeqK�X   test_batch_sizeqK�X   modelqX   resnetqX   use_swimqKX   out_dimqM X   temperatureqG?�      X   model_buffer_sizeqKX   krq G?ٙ�����X   async_weight_strategyq!X   local_roundsq"uu.PKM�-�  �  PK                    M $ train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/.format_versionFB  ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    P A train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/.storage_alignmentFB= ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    G 	 train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/byteorderFB ZZZZZlittlePK�=�      PK                    E  train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/versionFB ZZZ3
PKўgU      PK                    T < train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/.data/serialization_idFB8 ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400052285449422643447PKE2Ǎ(   (   PK          M�-�  �  F                 train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/data.pklPK          ��܃      M             q  train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/.format_versionPK          ?wq�      P               train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/.storage_alignmentPK          �=�      G             �  train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/byteorderPK          ўgU      E             V  train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/versionPK          E2Ǎ(   (   T             �  train_batch_size_128_new_results_cifar10_noniid_swim1_100_0.1/.data/serialization_idPK,       -                       �      �	      PK    �         PK      �  �	    