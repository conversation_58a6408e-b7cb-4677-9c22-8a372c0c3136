# SWIM-APPAFL集成模型定义（完全基于原始SWIM的CIFAR-100模型）
# 分为投影头版本和非投影头版本，确保与原始SWIM完全一致
import torch
import torch.nn as nn
import torch.nn.functional as F
import math


# ===== 原始SWIM的SimpleCNN实现 =====

class SimpleCNN_header(nn.Module):
    """
    原始SWIM的SimpleCNN_header实现
    用于CIFAR数据集的简单CNN特征提取器（不包含最后的分类层）
    """
    def __init__(self, input_dim, hidden_dims, output_dim=10):
        super(SimpleCNN_header, self).__init__()
        self.conv1 = nn.Conv2d(3, 6, 5)
        self.relu = nn.ReLU()
        self.pool = nn.MaxPool2d(2, 2)
        self.conv2 = nn.Conv2d(6, 16, 5)

        # 固定的网络结构：2个隐藏层
        self.fc1 = nn.Linear(input_dim, hidden_dims[0])
        self.fc2 = nn.Linear(hidden_dims[0], hidden_dims[1])
        # 注意：SimpleCNN_header不包含最后的分类层fc3

    def forward(self, x):
        x = self.pool(self.relu(self.conv1(x)))
        x = self.pool(self.relu(self.conv2(x)))
        x = x.view(-1, 16 * 5 * 5)

        x = self.relu(self.fc1(x))
        x = self.relu(self.fc2(x))
        # 注意：不包含最后的分类层
        return x


# ===== 原始SWIM的ResNet实现（适配CIFAR数据集）=====

def conv3x3(in_planes, out_planes, stride=1, groups=1, dilation=1):
    """3x3卷积层（带填充）"""
    return nn.Conv2d(in_planes, out_planes, kernel_size=3, stride=stride,
                     padding=dilation, groups=groups, bias=False, dilation=dilation)


def conv1x1(in_planes, out_planes, stride=1):
    """1x1卷积层"""
    return nn.Conv2d(in_planes, out_planes, kernel_size=1, stride=stride, bias=False)


class BasicBlock(nn.Module):
    """ResNet基础块（与原始SWIM完全一致）"""
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, downsample=None, groups=1,
                 base_width=64, dilation=1, norm_layer=None):
        super(BasicBlock, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        if groups != 1 or base_width != 64:
            raise ValueError('BasicBlock only supports groups=1 and base_width=64')
        if dilation > 1:
            raise NotImplementedError("Dilation > 1 not supported in BasicBlock")

        # 第一个卷积层（可能包含下采样）
        self.conv1 = conv3x3(inplanes, planes, stride)
        self.bn1 = norm_layer(planes)
        self.relu = nn.ReLU(inplace=True)

        # 第二个卷积层
        self.conv2 = conv3x3(planes, planes)
        self.bn2 = norm_layer(planes)

        # 下采样层（用于匹配残差连接的维度）
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        identity = x  # 保存输入用于残差连接

        # 第一个卷积块
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        # 第二个卷积块
        out = self.conv2(out)
        out = self.bn2(out)

        # 如果需要下采样，对identity进行变换
        if self.downsample is not None:
            identity = self.downsample(x)

        # 残差连接
        out += identity
        out = self.relu(out)

        return out


class Bottleneck(nn.Module):
    """ResNet瓶颈块（用于ResNet50/101/152）"""
    expansion = 4

    def __init__(self, inplanes, planes, stride=1, downsample=None, groups=1,
                 base_width=64, dilation=1, norm_layer=None):
        super(Bottleneck, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        width = int(planes * (base_width / 64.)) * groups

        # 1x1卷积（降维）
        self.conv1 = nn.Conv2d(inplanes, width, kernel_size=1, bias=False)
        self.bn1 = norm_layer(width)

        # 3x3卷积（主要特征提取）
        self.conv2 = nn.Conv2d(width, width, kernel_size=3, stride=stride,
                               padding=dilation, groups=groups, bias=False, dilation=dilation)
        self.bn2 = norm_layer(width)

        # 1x1卷积（升维）
        self.conv3 = nn.Conv2d(width, planes * self.expansion, kernel_size=1, bias=False)
        self.bn3 = norm_layer(planes * self.expansion)

        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        identity = x

        # 1x1卷积（降维）
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        # 3x3卷积（特征提取）
        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)

        # 1x1卷积（升维）
        out = self.conv3(out)
        out = self.bn3(out)

        # 下采样处理
        if self.downsample is not None:
            identity = self.downsample(x)

        # 残差连接
        out += identity
        out = self.relu(out)

        return out


class ResNetCifar10(nn.Module):
    """
    适配CIFAR数据集的ResNet模型（与原始SWIM完全一致）
    相比标准ResNet，第一层卷积使用3x3卷积且步长为1，适合32x32的小图像
    """

    def __init__(self, block, layers, num_classes=100, zero_init_residual=False,
                 groups=1, width_per_group=64, replace_stride_with_dilation=None,
                 norm_layer=None):
        super(ResNetCifar10, self).__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        self._norm_layer = norm_layer

        self.inplanes = 64
        self.dilation = 1
        if replace_stride_with_dilation is None:
            replace_stride_with_dilation = [False, False, False]
        if len(replace_stride_with_dilation) != 3:
            raise ValueError("replace_stride_with_dilation should be None "
                             "or a 3-element tuple, got {}".format(replace_stride_with_dilation))

        self.groups = groups
        self.base_width = width_per_group

        # 第一层卷积（适配CIFAR的32x32图像，使用3x3卷积，步长1）
        self.conv1 = nn.Conv2d(3, self.inplanes, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn1 = norm_layer(self.inplanes)
        self.relu = nn.ReLU(inplace=True)

        # 四个残差层
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2,
                                       dilate=replace_stride_with_dilation[0])
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2,
                                       dilate=replace_stride_with_dilation[1])
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2,
                                       dilate=replace_stride_with_dilation[2])

        # 全局平均池化和分类层
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(512 * block.expansion, num_classes)

        # 权重初始化
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

        if zero_init_residual:
            for m in self.modules():
                if isinstance(m, BasicBlock):
                    nn.init.constant_(m.bn2.weight, 0)

    def _make_layer(self, block, planes, blocks, stride=1, dilate=False):
        norm_layer = self._norm_layer
        downsample = None
        previous_dilation = self.dilation
        if dilate:
            self.dilation *= stride
            stride = 1
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                conv1x1(self.inplanes, planes * block.expansion, stride),
                norm_layer(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample, self.groups,
                            self.base_width, previous_dilation, norm_layer))
        self.inplanes = planes * block.expansion
        for _ in range(1, blocks):
            layers.append(block(self.inplanes, planes, groups=self.groups,
                                base_width=self.base_width, dilation=self.dilation,
                                norm_layer=norm_layer))

        return nn.Sequential(*layers)

    def _forward_impl(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.fc(x)

        return x

    def forward(self, x):
        return self._forward_impl(x)


def ResNet18_cifar10(**kwargs):
    """
    创建适配CIFAR数据集的ResNet-18模型（与原始SWIM完全一致）

    Args:
        **kwargs: 传递给ResNetCifar10的其他参数

    Returns:
        ResNetCifar10: ResNet-18模型实例
    """
    return ResNetCifar10(BasicBlock, [2, 2, 2, 2], **kwargs)


def ResNet50_cifar10(**kwargs):
    """
    创建适配CIFAR数据集的ResNet-50模型（与原始SWIM完全一致）

    Args:
        **kwargs: 传递给ResNetCifar10的其他参数

    Returns:
        ResNetCifar10: ResNet-50模型实例
    """
    return ResNetCifar10(Bottleneck, [3, 4, 6, 3], **kwargs)


# 为了向后兼容，保留原来的函数名
def ResNet18_cifar(num_classes=100):
    """创建适配CIFAR数据集的ResNet-18模型（向后兼容）"""
    return ResNet18_cifar10(num_classes=num_classes)


def ResNet50_cifar(num_classes=100):
    """创建适配CIFAR数据集的ResNet-50模型（向后兼容）"""
    return ResNet50_cifar10(num_classes=num_classes)


# ===== 原始SWIM的联邦对比学习模型（带投影头）=====

class ModelFedCon_SWIM(nn.Module):
    """
    原始SWIM的联邦对比学习模型（带投影头版本）
    完全复制原始SWIM的ModelFedCon实现，用于CIFAR-100
    """

    def __init__(self, base_model="resnet18", out_dim=256, n_classes=100, net_configs=None):
        super(ModelFedCon_SWIM, self).__init__()

        # 根据基础模型类型选择特征提取器（与原始SWIM完全一致）
        if base_model == "resnet50" or base_model == "resnet50-cifar100" or base_model == "resnet50-cifar10" or base_model == "resnet50-smallkernel":
            basemodel = ResNet50_cifar10()
            # 移除最后的分类层，只保留特征提取部分
            self.features = nn.Sequential(*list(basemodel.children())[:-1])
            num_ftrs = basemodel.fc.in_features
        elif base_model == "resnet18" or base_model == "resnet18-cifar100" or base_model == "resnet18-cifar10":
            basemodel = ResNet18_cifar10()
            # 移除最后的分类层，只保留特征提取部分
            self.features = nn.Sequential(*list(basemodel.children())[:-1])
            num_ftrs = basemodel.fc.in_features
        elif base_model == 'simple-cnn':
            self.features = SimpleCNN_header(input_dim=(16 * 5 * 5), hidden_dims=[120, 84], output_dim=n_classes)
            num_ftrs = 84
        else:
            raise ValueError(f"不支持的基础模型: {base_model}")

        # 投影MLP（用于对比学习，与原始SWIM完全一致）
        self.l1 = nn.Linear(num_ftrs, num_ftrs)  # 第一个投影层
        self.l2 = nn.Linear(num_ftrs, out_dim)   # 第二个投影层

        # 分类层（与原始SWIM完全一致）
        self.l3 = nn.Linear(out_dim, n_classes)

    def forward(self, x):
        """
        前向传播（与原始SWIM完全一致）
        Args:
            x: 输入张量
        Returns:
            h: 特征表示（用于对比学习）
            x: 投影特征（用于对比学习）
            y: 分类输出
        """
        # 特征提取
        h = self.features(x)
        h = torch.flatten(h, 1)  # 展平特征，保持批次维度

        # 投影层（用于对比学习）
        x = self.l1(h)      # 第一个投影层
        x = F.relu(x)       # ReLU激活
        x = self.l2(x)      # 第二个投影层

        # 分类层
        y = self.l3(x)

        return h, x, y  # 返回特征、投影特征和分类结果


# ===== 原始SWIM的联邦对比学习模型（无投影头）=====

class ModelFedCon_SWIM_NoHeader(nn.Module):
    """
    原始SWIM的联邦对比学习模型（无投影头版本）
    完全复制原始SWIM的ModelFedCon_noheader实现，用于APPAFL标准训练
    """

    def __init__(self, base_model="resnet18", out_dim=256, n_classes=100, net_configs=None):
        super(ModelFedCon_SWIM_NoHeader, self).__init__()

        # 根据基础模型类型选择特征提取器（与原始SWIM完全一致）
        if base_model == "resnet50" or base_model == "resnet50-cifar100" or base_model == "resnet50-cifar10" or base_model == "resnet50-smallkernel":
            basemodel = ResNet50_cifar10()
            # 移除最后的分类层，只保留特征提取部分
            self.features = nn.Sequential(*list(basemodel.children())[:-1])
            num_ftrs = basemodel.fc.in_features
        elif base_model == "resnet18" or base_model == "resnet18-cifar100" or base_model == "resnet18-cifar10":
            basemodel = ResNet18_cifar10()
            # 移除最后的分类层，只保留特征提取部分
            self.features = nn.Sequential(*list(basemodel.children())[:-1])
            num_ftrs = basemodel.fc.in_features
        elif base_model == 'simple-cnn':
            self.features = SimpleCNN_header(input_dim=(16 * 5 * 5), hidden_dims=[120, 84], output_dim=n_classes)
            num_ftrs = 84
        else:
            raise ValueError(f"不支持的基础模型: {base_model}")

        # 注意：此版本不使用投影头，直接从特征到分类（与原始SWIM完全一致）
        # 分类层（直接从特征映射到类别）
        self.l3 = nn.Linear(num_ftrs, n_classes)

    def forward(self, x):
        """
        前向传播（与原始SWIM的无投影头版本完全一致）
        Args:
            x: 输入张量
        Returns:
            h: 特征表示
            None: 占位符（保持接口一致性）
            y: 分类输出
        """
        # 特征提取
        h = self.features(x)
        h = torch.flatten(h, 1)  # 展平特征，保持批次维度

        # 直接分类（不使用投影头）
        y = self.l3(h)

        return h, None, y  # 返回特征、None（占位符）和分类结果
